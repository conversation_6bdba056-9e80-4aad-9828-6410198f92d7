# 好氧池运行参数计算API测试文档

## 概述

本测试套件提供了完整的好氧池运行参数计算API接口，模拟从数据获取到处理的完整流程。包含服务器端API、客户端测试和自动化测试脚本。

## 文件说明

### 1. `test_aerobic_api_server.py` - API服务器
提供RESTful API接口，模拟真实的数据获取和处理环境。

**主要功能：**
- 模拟摄像头配置管理
- 模拟传感器数据获取
- 好氧池数据分析计算
- 大模型分析结果生成

**可用接口：**
- `GET /api/health` - 健康检查
- `GET /api/cameras/config` - 获取摄像头配置
- `GET /api/sensor/data/<camera_id>` - 获取传感器数据
- `POST /api/aerobic/analyze` - 完整数据分析
- `POST /api/aerobic/simple` - 简化数据分析

### 2. `test_api_client.py` - 客户端测试
演示如何调用API接口进行数据获取和分析。

**测试内容：**
- 健康检查测试
- 摄像头配置获取测试
- 传感器数据获取测试
- 完整数据分析测试
- 简化数据分析测试
- 错误处理测试

### 3. `run_api_test.py` - 自动化测试脚本
自动启动服务器并运行完整的测试流程。

## 快速开始

### 方法1：自动化测试（推荐）
```bash
# 运行完整的自动化测试
python run_api_test.py
```

### 方法2：手动测试
```bash
# 终端1：启动服务器
python test_aerobic_api_server.py

# 终端2：运行客户端测试
python test_api_client.py
```

### 方法3：使用curl命令测试
```bash
# 启动服务器后，可以使用curl测试各个接口

# 健康检查
curl http://localhost:5000/api/health

# 获取摄像头配置
curl http://localhost:5000/api/cameras/config

# 获取传感器数据
curl http://localhost:5000/api/sensor/data/aerobic_tank_001

# 完整数据分析
curl -X POST http://localhost:5000/api/aerobic/analyze \
  -H "Content-Type: application/json" \
  -d '{"camera_id":"aerobic_tank_001"}'

# 简化数据分析
curl -X POST http://localhost:5000/api/aerobic/simple \
  -H "Content-Type: application/json" \
  -d '{
    "yesterday_flow": 22.0,
    "today_mlss": 3500.0,
    "today_influent_bod": 82.3,
    "today_effluent_bod": 4.3
  }'
```

## API接口详细说明

### 1. 健康检查
```
GET /api/health
```
**响应示例：**
```json
{
  "code": 200,
  "message": "服务正常运行",
  "timestamp": "2025-08-27 16:30:00",
  "available_endpoints": [...]
}
```

### 2. 获取摄像头配置
```
GET /api/cameras/config
```
**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "camera_id": "aerobic_tank_001",
      "video_id": "AT001",
      "system_type": "aerobic_tank_monitoring",
      "location": "好氧池1号",
      "status": "active"
    }
  ]
}
```

### 3. 获取传感器数据
```
GET /api/sensor/data/<camera_id>
```
**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "today": {
      "BOD": 82.30,
      "出水BOD": 4.30,
      "MLSS": 3500.00,
      "timestamp": "2025-08-27 16:30:00"
    },
    "yesterday": {
      "Qy": 22.00,
      "Sy_BOD": 80.00,
      "Xy_MLSS": 3400.00,
      "timestamp": "2025-08-26 16:30:00"
    }
  }
}
```

### 4. 完整数据分析
```
POST /api/aerobic/analyze
Content-Type: application/json

{
  "camera_id": "aerobic_tank_001"
}
```
**响应示例：**
```json
{
  "code": 200,
  "message": "分析成功",
  "data": {
    "camera_id": "aerobic_tank_001",
    "analysis_time": "2025-08-27 16:30:00",
    "calculation_results": {
      "target_mlss": 3670.71,
      "current_mlss": 3500.00,
      "wasting_needed": true,
      "wasting_time_hours": 27.39
    },
    "sensor_data_used": {
      "today_influent_bod": 82.30,
      "today_effluent_bod": 4.30,
      "today_mlss": 3500.00,
      "yesterday_flow": 22.00
    },
    "analysis_detail": "详细分析...",
    "adjustment_suggestion": "调整建议...",
    "calculation_process": "计算过程..."
  }
}
```

### 5. 简化数据分析
```
POST /api/aerobic/simple
Content-Type: application/json

{
  "yesterday_flow": 22.0,
  "today_mlss": 3500.0,
  "today_influent_bod": 82.3,
  "today_effluent_bod": 4.3,
  "tank_volume": 44437.5,
  "pump_flow_rate": 75.0,
  "pump_count": 1
}
```

## 数据要求

### 必需参数（4个）
1. **yesterday_flow** - 昨天处理水量 (万m³/d)
2. **today_mlss** - 今天MLSS浓度 (mg/L)
3. **today_influent_bod** - 今天进水BOD浓度 (mg/L)
4. **today_effluent_bod** - 今天出水BOD浓度 (mg/L)

### 可选参数
- **tank_volume** - 好氧池体积 (m³)，默认44437.5
- **pump_flow_rate** - 单台排泥泵流量 (m³/h)，默认75.0
- **pump_count** - 排泥泵数量，默认1

### 辅助参数（用于大模型分析对比）
- **yesterday_influent_bod** - 昨天进水BOD浓度 (mg/L)
- **yesterday_mlss** - 昨天MLSS浓度 (mg/L)

## 计算原理

系统采用专业的污水处理工艺计算方法：

1. **产泥系数**: Y = 1.28 kg污泥/kg BOD去除量
2. **目标污泥负荷**: F/M = 0.111 kgBOD/(kgMLSS·d)
3. **污泥增量**: Vincrease = 1.28 × (进水BOD - 出水BOD) × 处理水量 / 1000
4. **目标MLSS**: Xtarget = 处理水量 × 进水BOD / (池体积 × 0.111)
5. **排泥时间**: T = 排泥干重 / 回流污泥浓度 / 排泥泵总流量 × 1000

## 注意事项

1. **数值精度**: 所有计算结果保持两位小数精度
2. **单位转换**: 系统自动处理万m³/d和m³/d的单位转换
3. **错误处理**: 缺少必需参数时会返回明确的错误信息
4. **数据验证**: 系统会验证输入数据的有效性

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口5000是否被占用
   - 确保安装了Flask依赖：`pip install flask`

2. **客户端连接失败**
   - 确保服务器已启动
   - 检查防火墙设置

3. **分析失败**
   - 检查输入参数是否完整
   - 确保数值格式正确

### 依赖安装
```bash
pip install flask requests
```

## 扩展说明

这个测试套件可以作为真实生产环境的原型，主要扩展点：

1. **数据库集成**: 替换内存数据为真实数据库
2. **认证授权**: 添加API认证机制
3. **监控日志**: 集成更完善的监控和日志系统
4. **负载均衡**: 支持多实例部署
5. **大模型集成**: 集成真实的大模型API

## 联系信息

如有问题或建议，请联系开发团队。
