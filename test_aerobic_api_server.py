#!/usr/bin/env python3
"""
好氧池运行参数计算API测试服务器
提供模拟数据接口，测试从数据获取到处理的完整流程
使用FastAPI框架
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import json
import logging
from datetime import datetime, timedelta
from server.task.aerobic_tank_ops import AerobicTankAnalyzer, format_number
from llms.config_sludge_discharge import SYSTEM_SLUDGE_DISCHARGE

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="好氧池运行参数计算API",
    description="提供好氧池数据获取、分析和排泥建议的API接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 定义请求模型
class AnalysisRequest(BaseModel):
    camera_id: str = "aerobic_tank_001"

class SimpleAnalysisRequest(BaseModel):
    yesterday_flow: float
    today_mlss: float
    today_influent_bod: float
    today_effluent_bod: float
    tank_volume: Optional[float] = 44437.5
    pump_flow_rate: Optional[float] = 75.0
    pump_count: Optional[int] = 1

# 定义响应模型
class ApiResponse(BaseModel):
    code: int
    message: str
    data: Optional[Any] = None

# 模拟数据库 - 存储模拟的传感器数据
MOCK_DATA = {
    "cameras": [
        {
            "camera_id": "aerobic_tank_001",
            "video_id": "AT001",
            "system_type": "aerobic_tank_monitoring",
            "location": "好氧池1号",
            "status": "active"
        },
        {
            "camera_id": "aerobic_tank_002", 
            "video_id": "AT002",
            "system_type": "aerobic_tank_monitoring",
            "location": "好氧池2号",
            "status": "active"
        }
    ],
    "sensor_data": {
        "aerobic_tank_001": {
            "today": {
                "BOD": 82.30,           # 今天进水BOD浓度 (mg/L)
                "出水BOD": 4.30,        # 今天出水BOD浓度 (mg/L)
                "MLSS": 3500.00,        # 今天MLSS浓度 (mg/L)
                "污水处理量": 22.00,     # 今天处理水量 (万m³/d) - 不参与计算
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "yesterday": {
                "Qy": 22.00,            # 昨天处理水量 (万m³/d) - 计算必需
                "Sy_BOD": 80.00,        # 昨天进水BOD浓度 (mg/L) - 可选
                "Xy_MLSS": 3400.00,     # 昨天MLSS浓度 (mg/L) - 可选
                "timestamp": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
            }
        },
        "aerobic_tank_002": {
            "today": {
                "BOD": 75.50,
                "出水BOD": 3.80,
                "MLSS": 3200.00,
                "污水处理量": 20.50,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "yesterday": {
                "Qy": 20.50,
                "Sy_BOD": 78.00,
                "Xy_MLSS": 3100.00,
                "timestamp": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
            }
        }
    },
    "equipment_params": {
        "tank_volume": 44437.5,     # 好氧池体积 (m³)
        "pump_flow_rate": 75.0,     # 单台排泥泵流量 (m³/h)
        "pump_count": 1             # 排泥泵数量
    }
}

@app.get("/api/cameras/config", response_model=ApiResponse)
async def get_cameras_config():
    """获取摄像头配置信息"""
    try:
        logger.info("获取摄像头配置信息")
        return ApiResponse(
            code=200,
            message="success",
            data=MOCK_DATA["cameras"]
        )
    except Exception as e:
        logger.error(f"获取摄像头配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/sensor/data/{camera_id}", response_model=ApiResponse)
async def get_sensor_data(camera_id: str):
    """获取指定摄像头的传感器数据"""
    try:
        logger.info(f"获取摄像头 {camera_id} 的传感器数据")

        if camera_id not in MOCK_DATA["sensor_data"]:
            raise HTTPException(
                status_code=404,
                detail=f"摄像头 {camera_id} 数据不存在"
            )

        sensor_data = MOCK_DATA["sensor_data"][camera_id]
        return ApiResponse(
            code=200,
            message="success",
            data=sensor_data
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取传感器数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/aerobic/analyze", response_model=ApiResponse)
async def analyze_aerobic_data(request: AnalysisRequest):
    """分析好氧池数据并返回排泥建议"""
    try:
        logger.info("开始分析好氧池数据")

        # 获取请求参数
        camera_id = request.camera_id

        # 获取传感器数据
        if camera_id not in MOCK_DATA["sensor_data"]:
            raise HTTPException(
                status_code=404,
                detail=f"摄像头 {camera_id} 数据不存在"
            )
        
        sensor_data = MOCK_DATA["sensor_data"][camera_id]
        equipment_params = MOCK_DATA["equipment_params"]
        
        # 构建分析数据格式
        aerobic_data = {
            camera_id: {
                "camera_id": camera_id,
                "today_parameters": {
                    "BOD": {
                        "point_id": "p002",
                        "value": sensor_data["today"]["BOD"],
                        "monitor_time": sensor_data["today"]["timestamp"]
                    },
                    "出水BOD": {
                        "point_id": "p004", 
                        "value": sensor_data["today"]["出水BOD"],
                        "monitor_time": sensor_data["today"]["timestamp"]
                    },
                    "mlss": {
                        "point_id": "p003",
                        "value": sensor_data["today"]["MLSS"],
                        "monitor_time": sensor_data["today"]["timestamp"]
                    }
                },
                "yesterday_parameters": {
                    "Qy": {
                        "point_id": "p001",
                        "value": sensor_data["yesterday"]["Qy"],
                        "monitor_time": sensor_data["yesterday"]["timestamp"]
                    },
                    "Sy_BOD": {
                        "point_id": "p002",
                        "value": sensor_data["yesterday"]["Sy_BOD"],
                        "monitor_time": sensor_data["yesterday"]["timestamp"]
                    },
                    "Xy_MLSS": {
                        "point_id": "p003",
                        "value": sensor_data["yesterday"]["Xy_MLSS"],
                        "monitor_time": sensor_data["yesterday"]["timestamp"]
                    }
                }
            }
        }
        
        # 执行分析
        analyzer = AerobicTankAnalyzer()
        analysis_results = analyzer.analyze_collected_data(
            aerobic_data,
            equipment_params["tank_volume"],
            equipment_params["pump_flow_rate"],
            equipment_params["pump_count"]
        )
        
        # 处理分析结果
        result = analysis_results.get(camera_id, {})
        
        if result.get("status") == "success":
            # 模拟大模型分析（简化版）
            llm_analysis = generate_mock_llm_analysis(result, sensor_data)
            
            response_data = {
                "camera_id": camera_id,
                "analysis_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "calculation_results": {
                    "target_mlss": result["X_target"],
                    "current_mlss": sensor_data["today"]["MLSS"],
                    "wasting_needed": result["wasting_needed"],
                    "wasting_time_hours": result["wasting_time_hours"]
                },
                "sensor_data_used": {
                    "today_influent_bod": sensor_data["today"]["BOD"],
                    "today_effluent_bod": sensor_data["today"]["出水BOD"],
                    "today_mlss": sensor_data["today"]["MLSS"],
                    "yesterday_flow": sensor_data["yesterday"]["Qy"]
                },
                "analysis_detail": llm_analysis["analysis_detail"],
                "adjustment_suggestion": llm_analysis["adjustment_suggestion"],
                "calculation_process": result["analysis"]
            }
            
            logger.info(f"分析完成 - 相机 {camera_id}, 排泥时间: {result['wasting_time_hours']} 小时")

            return ApiResponse(
                code=200,
                message="分析成功",
                data=response_data
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"分析失败: {result.get('message', '未知错误')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析好氧池数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

def generate_mock_llm_analysis(calculation_result, sensor_data):
    """生成模拟的大模型分析结果"""
    target_mlss = calculation_result["X_target"]
    current_mlss = sensor_data["today"]["MLSS"]
    wasting_time = calculation_result["wasting_time_hours"]
    wasting_needed = calculation_result["wasting_needed"]
    
    if wasting_needed:
        analysis_detail = f"""基于当前数据分析：
今天处理水量{format_number(sensor_data['yesterday']['Qy'])}万m³/d，进水BOD浓度{format_number(sensor_data['today']['BOD'])}mg/L，出水BOD浓度{format_number(sensor_data['today']['出水BOD'])}mg/L，当前MLSS浓度{format_number(current_mlss)}mg/L。

根据专业工艺计算（产泥系数1.28，目标F/M=0.111），目标MLSS浓度为{format_number(target_mlss)}mg/L，当前MLSS浓度{'高于' if current_mlss > target_mlss else '低于'}目标值{format_number(abs(current_mlss - target_mlss))}mg/L。

BOD去除效果良好，去除率达到{format_number((sensor_data['today']['BOD'] - sensor_data['today']['出水BOD']) / sensor_data['today']['BOD'] * 100)}%。系统运行稳定，需要进行排泥操作以维持最佳MLSS浓度。"""

        adjustment_suggestion = f"""建议执行排泥操作{format_number(wasting_time)}小时。

操作建议：
1. 采用间歇性排泥方式，每次排泥2-3小时，间隔4-6小时
2. 监控DO浓度保持在2.0-4.0mg/L
3. 内回流比控制在100-120%，外回流比维持在75-85%
4. 预期排泥后MLSS浓度降至{format_number(target_mlss)}mg/L左右

注意事项：
- 避免一次性大量排泥对工艺系统造成冲击
- 排泥过程中密切监控出水水质变化
- 如发现异常立即停止排泥并检查系统状态"""
    else:
        analysis_detail = f"""当前MLSS浓度{format_number(current_mlss)}mg/L低于目标浓度{format_number(target_mlss)}mg/L，系统污泥浓度适宜，无需进行排泥操作。

BOD去除效果正常，系统运行稳定。建议继续监控MLSS浓度变化趋势。"""

        adjustment_suggestion = f"""当前无需排泥，建议：
1. 继续监控MLSS浓度变化
2. 保持当前运行参数
3. 关注进水水质变化
4. 定期检查设备运行状态"""
    
    return {
        "analysis_detail": analysis_detail,
        "adjustment_suggestion": adjustment_suggestion
    }

@app.post("/api/aerobic/simple", response_model=ApiResponse)
async def analyze_simple_data(request: SimpleAnalysisRequest):
    """使用简化数据格式进行分析"""
    try:
        # 构建分析数据
        simple_data = {
            'tank_volume': request.tank_volume,
            'yesterday_flow': request.yesterday_flow,
            'today_mlss': request.today_mlss,
            'today_influent_bod': request.today_influent_bod,
            'today_effluent_bod': request.today_effluent_bod,
            'pump_flow_rate': request.pump_flow_rate,
            'pump_count': request.pump_count
        }

        # 执行分析
        analyzer = AerobicTankAnalyzer()
        result = analyzer.analyze_simple_data_format(simple_data)

        # 获取第一个结果
        first_result = next(iter(result.values()))

        if first_result["status"] == "success":
            return ApiResponse(
                code=200,
                message="分析成功",
                data={
                    "target_mlss": first_result["X_target"],
                    "wasting_needed": first_result["wasting_needed"],
                    "wasting_time_hours": first_result["wasting_time_hours"],
                    "analysis": first_result["analysis"],
                    "recommendation": first_result["recommendation"]
                }
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"分析失败: {first_result.get('message', '未知错误')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"简化数据分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health", response_model=ApiResponse)
async def health_check():
    """健康检查接口"""
    return ApiResponse(
        code=200,
        message="服务正常运行",
        data={
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "available_endpoints": [
                "GET /api/cameras/config - 获取摄像头配置",
                "GET /api/sensor/data/<camera_id> - 获取传感器数据",
                "POST /api/aerobic/analyze - 完整数据分析",
                "POST /api/aerobic/simple - 简化数据分析",
                "GET /api/health - 健康检查",
                "GET /docs - API文档",
                "GET /redoc - ReDoc文档"
            ]
        }
    )

if __name__ == '__main__':
    import uvicorn

    print("=" * 60)
    print("好氧池运行参数计算API测试服务器 (FastAPI)")
    print("=" * 60)
    print("可用接口:")
    print("  GET  /api/health - 健康检查")
    print("  GET  /api/cameras/config - 获取摄像头配置")
    print("  GET  /api/sensor/data/<camera_id> - 获取传感器数据")
    print("  POST /api/aerobic/analyze - 完整数据分析")
    print("  POST /api/aerobic/simple - 简化数据分析")
    print("  GET  /docs - Swagger API文档")
    print("  GET  /redoc - ReDoc API文档")
    print("=" * 60)
    print("测试示例:")
    print("  curl http://localhost:8000/api/health")
    print("  curl http://localhost:8000/api/cameras/config")
    print("  curl http://localhost:8000/api/sensor/data/aerobic_tank_001")
    print("  curl -X POST http://localhost:8000/api/aerobic/analyze -H 'Content-Type: application/json' -d '{\"camera_id\":\"aerobic_tank_001\"}'")
    print("=" * 60)
    print("API文档地址:")
    print("  Swagger UI: http://localhost:8000/docs")
    print("  ReDoc: http://localhost:8000/redoc")
    print("=" * 60)
    print("启动服务器...")

    uvicorn.run("test_aerobic_api_server:app", host="0.0.0.0", port=8006, reload=True)
