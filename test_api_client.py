#!/usr/bin/env python3
"""
好氧池API客户端测试脚本
演示如何调用API接口进行数据获取和分析
"""

import requests
import json
import time

# API服务器地址 (FastAPI默认端口8000)
BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print("✓ 服务器运行正常")
            print(f"  时间戳: {data['timestamp']}")
            print("  可用接口:")
            for endpoint in data['available_endpoints']:
                print(f"    {endpoint}")
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def test_cameras_config():
    """测试获取摄像头配置"""
    print("\n=== 测试获取摄像头配置 ===")
    try:
        response = requests.get(f"{BASE_URL}/api/cameras/config")
        if response.status_code == 200:
            data = response.json()
            print("✓ 获取摄像头配置成功")
            cameras = data['data']
            print(f"  摄像头数量: {len(cameras)}")
            for camera in cameras:
                print(f"    ID: {camera['camera_id']}, 位置: {camera['location']}, 状态: {camera['status']}")
            return cameras
        else:
            print(f"✗ 获取摄像头配置失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return []

def test_sensor_data(camera_id):
    """测试获取传感器数据"""
    print(f"\n=== 测试获取传感器数据 - {camera_id} ===")
    try:
        response = requests.get(f"{BASE_URL}/api/sensor/data/{camera_id}")
        if response.status_code == 200:
            data = response.json()
            print("✓ 获取传感器数据成功")
            sensor_data = data['data']
            
            print("  今天数据:")
            for key, value in sensor_data['today'].items():
                if key != 'timestamp':
                    print(f"    {key}: {value}")
            print(f"    时间戳: {sensor_data['today']['timestamp']}")
            
            print("  昨天数据:")
            for key, value in sensor_data['yesterday'].items():
                if key != 'timestamp':
                    print(f"    {key}: {value}")
            print(f"    时间戳: {sensor_data['yesterday']['timestamp']}")
            
            return sensor_data
        else:
            print(f"✗ 获取传感器数据失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

def test_aerobic_analysis(camera_id):
    """测试好氧池数据分析"""
    print(f"\n=== 测试好氧池数据分析 - {camera_id} ===")
    try:
        payload = {"camera_id": camera_id}
        response = requests.post(
            f"{BASE_URL}/api/aerobic/analyze",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 分析成功")
            result = data['data']
            
            print("  计算结果:")
            calc_results = result['calculation_results']
            print(f"    目标MLSS浓度: {calc_results['target_mlss']} mg/L")
            print(f"    当前MLSS浓度: {calc_results['current_mlss']} mg/L")
            print(f"    是否需要排泥: {'是' if calc_results['wasting_needed'] else '否'}")
            print(f"    排泥时间: {calc_results['wasting_time_hours']} 小时")
            
            print("  使用的传感器数据:")
            sensor_used = result['sensor_data_used']
            for key, value in sensor_used.items():
                print(f"    {key}: {value}")
            
            print("  分析详情:")
            print(f"    {result['analysis_detail'][:100]}...")
            
            print("  调整建议:")
            print(f"    {result['adjustment_suggestion'][:100]}...")
            
            return result
        else:
            print(f"✗ 分析失败: {response.status_code}")
            if response.text:
                error_data = response.json()
                print(f"    错误信息: {error_data.get('message', '未知错误')}")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

def test_simple_analysis():
    """测试简化数据分析"""
    print("\n=== 测试简化数据分析 ===")
    try:
        # 只使用4个必需参数
        payload = {
            "yesterday_flow": 22.0,         # 昨天处理水量 (万m³/d)
            "today_mlss": 3500.0,           # 今天MLSS浓度 (mg/L)
            "today_influent_bod": 82.3,     # 今天进水BOD浓度 (mg/L)
            "today_effluent_bod": 4.3,      # 今天出水BOD浓度 (mg/L)
            # 可选参数
            "tank_volume": 44437.5,
            "pump_flow_rate": 75.0,
            "pump_count": 1
        }
        
        print("  输入参数:")
        for key, value in payload.items():
            print(f"    {key}: {value}")
        
        response = requests.post(
            f"{BASE_URL}/api/aerobic/simple",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 简化分析成功")
            result = data['data']
            
            print("  分析结果:")
            print(f"    目标MLSS浓度: {result['target_mlss']} mg/L")
            print(f"    是否需要排泥: {'是' if result['wasting_needed'] else '否'}")
            print(f"    排泥时间: {result['wasting_time_hours']} 小时")
            
            print("  计算过程:")
            analysis_lines = result['analysis'].split('\n')[:3]
            for line in analysis_lines:
                if line.strip():
                    print(f"    {line}")
            
            return result
        else:
            print(f"✗ 简化分析失败: {response.status_code}")
            if response.text:
                error_data = response.json()
                print(f"    错误信息: {error_data.get('message', '未知错误')}")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

def test_missing_params():
    """测试缺少必需参数的情况"""
    print("\n=== 测试缺少必需参数 ===")
    try:
        # 故意缺少必需参数
        payload = {
            "yesterday_flow": 22.0,
            "today_mlss": 3500.0,
            # 缺少 today_influent_bod 和 today_effluent_bod
        }
        
        response = requests.post(
            f"{BASE_URL}/api/aerobic/simple",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 400:
            data = response.json()
            print("✓ 正确识别缺少必需参数")
            print(f"    错误信息: {data.get('message', '未知错误')}")
        else:
            print(f"✗ 未正确处理缺少参数的情况: {response.status_code}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")

def main():
    """主测试函数"""
    print("好氧池API客户端测试")
    print("=" * 60)
    
    # 1. 健康检查
    if not test_health_check():
        print("服务器未启动，请先运行: python test_aerobic_api_server.py")
        return
    
    # 等待一秒
    time.sleep(1)
    
    # 2. 获取摄像头配置
    cameras = test_cameras_config()
    if not cameras:
        print("无法获取摄像头配置")
        return
    
    # 3. 获取传感器数据
    camera_id = cameras[0]['camera_id']
    sensor_data = test_sensor_data(camera_id)
    
    # 4. 完整数据分析
    analysis_result = test_aerobic_analysis(camera_id)
    
    # 5. 简化数据分析
    simple_result = test_simple_analysis()
    
    # 6. 测试错误处理
    test_missing_params()
    
    print("\n" + "=" * 60)
    print("✓ 所有测试完成")
    print("✓ API接口工作正常")
    print("✓ 数据获取到处理流程验证成功")
    
    # 对比两种分析方法的结果
    if analysis_result and simple_result:
        print("\n=== 结果对比 ===")
        print(f"完整分析排泥时间: {analysis_result['calculation_results']['wasting_time_hours']} 小时")
        print(f"简化分析排泥时间: {simple_result['wasting_time_hours']} 小时")
        print("✓ 两种方法结果一致" if abs(
            analysis_result['calculation_results']['wasting_time_hours'] - 
            simple_result['wasting_time_hours']
        ) < 0.01 else "✗ 结果不一致")

if __name__ == "__main__":
    main()
