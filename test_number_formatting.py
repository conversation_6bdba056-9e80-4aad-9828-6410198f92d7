#!/usr/bin/env python3
"""
测试好氧池运行参数计算模块的数值格式化功能
验证所有数值计算都保持两位小数输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server.task.aerobic_tank_ops import format_number, AerobicTankAnalyzer

def test_format_number():
    """测试数值格式化函数"""
    print("=== 测试数值格式化函数 ===")
    
    test_cases = [
        (123.456, 2, "123.46"),
        (123.456, 3, "123.456"),
        (123, 2, "123.00"),
        (0, 2, "0.00"),
        (None, 2, "0.00"),
        ("invalid", 2, "0.00"),
        (123.999, 2, "124.00"),  # 测试四舍五入
    ]
    
    for value, decimal_places, expected in test_cases:
        result = format_number(value, decimal_places)
        status = "✓" if result == expected else "✗"
        print(f"{status} format_number({value}, {decimal_places}) = {result} (期望: {expected})")

def test_aerobic_calculation():
    """测试好氧池计算的数值格式化"""
    print("\n=== 测试好氧池计算的数值格式化 ===")
    
    analyzer = AerobicTankAnalyzer()
    
    # 测试数据
    simple_data = {
        'tank_volume': 44437.5,
        'yesterday_flow': 22.0,  # 万m³/d
        'today_mlss': 3500.0,
        'today_influent_bod': 82.3,
        'today_effluent_bod': 4.3,
        'pump_flow_rate': 75.0,
        'pump_count': 1
    }
    
    print("输入数据:")
    for key, value in simple_data.items():
        print(f"  {key}: {value}")
    
    # 执行分析
    result = analyzer.analyze_simple_data_format(simple_data)
    
    # 检查结果
    for camera_id, analysis in result.items():
        if analysis['status'] == 'success':
            print(f"\n相机 {camera_id} 分析结果:")
            print(f"  目标MLSS浓度: {analysis['X_target']} mg/L")
            print(f"  排泥时间: {analysis['wasting_time_hours']} 小时")
            print(f"  是否需要排泥: {'是' if analysis['wasting_needed'] else '否'}")
            
            # 检查分析过程中的数值格式
            analysis_lines = analysis['analysis'].split('\n')
            print("\n分析过程中的数值格式检查:")
            for i, line in enumerate(analysis_lines[:5], 1):
                if line.strip():
                    print(f"  {i}. {line}")
                    
            # 验证关键数值都是两位小数格式
            x_target = analysis['X_target']
            wasting_time = analysis['wasting_time_hours']
            
            # 检查是否为两位小数
            x_target_str = str(x_target)
            wasting_time_str = str(wasting_time) if wasting_time is not None else "None"
            
            print(f"\n数值格式验证:")
            if '.' in x_target_str:
                decimal_places = len(x_target_str.split('.')[1])
                print(f"  目标MLSS浓度小数位数: {decimal_places} ({'✓' if decimal_places == 2 else '✗'})")
            
            if wasting_time is not None and '.' in wasting_time_str:
                decimal_places = len(wasting_time_str.split('.')[1])
                print(f"  排泥时间小数位数: {decimal_places} ({'✓' if decimal_places == 2 else '✗'})")
        else:
            print(f"分析失败: {analysis.get('message', '未知错误')}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    analyzer = AerobicTankAnalyzer()
    
    # 测试不需要排泥的情况
    no_wasting_data = {
        'tank_volume': 44437.5,
        'yesterday_flow': 22.0,
        'today_mlss': 2000.0,  # 较低的MLSS
        'today_influent_bod': 50.0,
        'today_effluent_bod': 5.0,
        'pump_flow_rate': 75.0,
        'pump_count': 1
    }
    
    print("测试不需要排泥的情况:")
    result = analyzer.analyze_simple_data_format(no_wasting_data)
    
    for camera_id, analysis in result.items():
        if analysis['status'] == 'success':
            print(f"  目标MLSS浓度: {analysis['X_target']} mg/L")
            print(f"  当前MLSS浓度: {no_wasting_data['today_mlss']} mg/L")
            print(f"  是否需要排泥: {'是' if analysis['wasting_needed'] else '否'}")
            print(f"  排泥时间: {analysis['wasting_time_hours']} 小时")

if __name__ == "__main__":
    print("好氧池运行参数计算模块 - 数值格式化测试")
    print("=" * 60)
    
    test_format_number()
    test_aerobic_calculation()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("测试完成！所有数值计算都应该保持两位小数输出。")
