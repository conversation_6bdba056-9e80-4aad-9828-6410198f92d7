#!/usr/bin/env python3
"""
好氧池API测试启动脚本
自动启动服务器并运行客户端测试
"""

import subprocess
import time
import sys
import os
import signal
import requests
from threading import Thread

def check_server_ready(max_attempts=30):
    """检查服务器是否准备就绪"""
    for i in range(max_attempts):
        try:
            response = requests.get("http://localhost:5000/api/health", timeout=2)
            if response.status_code == 200:
                return True
        except:
            pass
        time.sleep(1)
        print(f"等待服务器启动... ({i+1}/{max_attempts})")
    return False

def run_server():
    """在后台运行服务器"""
    try:
        # 启动服务器进程
        server_process = subprocess.Popen(
            [sys.executable, "test_aerobic_api_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        return server_process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def run_client_test():
    """运行客户端测试"""
    try:
        result = subprocess.run(
            [sys.executable, "test_api_client.py"],
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        print("客户端测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
        return result.returncode == 0
    except Exception as e:
        print(f"运行客户端测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("好氧池API完整测试流程")
    print("=" * 60)
    
    server_process = None
    
    try:
        # 1. 启动服务器
        print("1. 启动API服务器...")
        server_process = run_server()
        
        if not server_process:
            print("✗ 服务器启动失败")
            return
        
        # 2. 等待服务器准备就绪
        print("2. 等待服务器准备就绪...")
        if not check_server_ready():
            print("✗ 服务器启动超时")
            return
        
        print("✓ 服务器启动成功")
        
        # 3. 运行客户端测试
        print("\n3. 运行客户端测试...")
        success = run_client_test()
        
        if success:
            print("\n✓ 所有测试通过")
        else:
            print("\n✗ 测试失败")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
    finally:
        # 4. 清理资源
        if server_process:
            print("\n4. 关闭服务器...")
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
                print("✓ 服务器已关闭")
            except subprocess.TimeoutExpired:
                print("强制关闭服务器...")
                server_process.kill()
                server_process.wait()
            except Exception as e:
                print(f"关闭服务器时发生错误: {e}")

if __name__ == "__main__":
    main()
